"""
Main FastAPI REST API Service
Orchestrates all microservices and provides public API endpoints
"""

from fastapi import FastAP<PERSON>, HTTPException, Depends, BackgroundTasks, status
from fastapi.responses import JSONResponse, FileResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import os
import httpx
from typing import List, Optional
from urllib.parse import unquote
from sqlalchemy.orm import Session

from shared.schemas.api import ProcessEmailsRequest, CUFEResponse, CUFEListResponse
from shared.schemas.mailbox import EmailProcessRequest, EmailProcessResponse
from shared.schemas.auth import LoginRequest, LoginResponse, UserCreate, ClientCreate
from shared.schemas.extraction import InvoiceLineItemData, InvoiceAllowanceChargeData, InvoicePaymentTermData
from shared.database.connection import get_db, create_tables
from shared.database.models import <PERSON><PERSON><PERSON><PERSON><PERSON>ord, EmailRecord, User, <PERSON><PERSON>, InvoiceLineItem, InvoiceAllowanceCharge, InvoicePaymentTerm
from shared.utils.logger import get_logger
from shared.utils.auth import (
    verify_token, create_user_token, authenticate_user,
    get_current_user, get_current_client, security
)
from shared.utils.password import hash_password

# Initialize FastAPI app
app = FastAPI(
    title="CUFE Extraction API",
    description="Main API for CUFE extraction automation system",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware for frontend integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure this properly for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

logger = get_logger(__name__)

# Service URLs
MAILBOX_SERVICE_URL = os.getenv("MAILBOX_SERVICE_URL", "http://localhost:8001")
FILE_PROCESSING_SERVICE_URL = os.getenv("FILE_PROCESSING_SERVICE_URL", "http://localhost:8002")
EXTRACTION_SERVICE_URL = os.getenv("EXTRACTION_SERVICE_URL", "http://localhost:8003")

def _convert_cufe_record_to_response(cufe_record: CUFERecord, db: Session) -> CUFEResponse:
    """
    Convert a CUFERecord database model to a CUFEResponse with all comprehensive invoice data
    """
    # Convert line items
    line_items = []
    for line_item in cufe_record.invoice_line_items:
        line_items.append(InvoiceLineItemData(
            line_number=line_item.line_number,
            item_name=line_item.item_name,
            item_description=line_item.item_description,
            item_code=line_item.item_code,
            invoiced_quantity=line_item.invoiced_quantity,
            unit_of_measure=line_item.unit_of_measure,
            unit_price=line_item.unit_price,
            line_extension_amount=line_item.line_extension_amount,
            line_tax_amount=line_item.line_tax_amount,
            line_tax_inclusive_amount=line_item.line_tax_inclusive_amount,
            allowance_charge_amount=line_item.allowance_charge_amount,
            free_of_charge_indicator=line_item.free_of_charge_indicator
        ))

    # Convert allowance charges
    allowance_charges = []
    for ac in cufe_record.allowance_charges:
        allowance_charges.append(InvoiceAllowanceChargeData(
            charge_indicator=ac.charge_indicator,
            allowance_charge_reason_code=ac.allowance_charge_reason_code,
            allowance_charge_reason=ac.allowance_charge_reason,
            multiplier_factor_numeric=ac.multiplier_factor_numeric,
            amount=ac.amount,
            base_amount=ac.base_amount,
            tax_category=ac.tax_category,
            tax_amount=ac.tax_amount
        ))

    # Convert payment terms
    payment_terms = []
    for pt in cufe_record.payment_terms:
        payment_terms.append(InvoicePaymentTermData(
            payment_means_code=pt.payment_means_code,
            payment_due_date=pt.payment_due_date,
            payment_terms_note=pt.payment_terms_note,
            settlement_period_measure=pt.settlement_period_measure,
            settlement_period_unit=pt.settlement_period_unit,
            settlement_discount_percent=pt.settlement_discount_percent,
            penalty_surcharge_percent=pt.penalty_surcharge_percent,
            amount=pt.amount
        ))

    return CUFEResponse(
        cufe_value=cufe_record.cufe_value,
        email_id=cufe_record.email_record.email_id,
        reception_date=cufe_record.email_record.reception_date,
        xml_file_path=cufe_record.xml_file_record.file_path if cufe_record.xml_file_record else "",
        pdf_file_path=cufe_record.pdf_file_record.file_path if cufe_record.pdf_file_record else None,
        processed_date=cufe_record.extraction_date,

        # Basic invoice information
        issuer_name=cufe_record.issuer_name,
        document_number=cufe_record.document_number,
        issue_date=cufe_record.issue_date,
        total_amount=cufe_record.total_amount,

        # Enhanced tax and monetary details
        tax_exclusive_amount=cufe_record.tax_exclusive_amount,
        tax_inclusive_amount=cufe_record.tax_inclusive_amount,
        allowance_total_amount=cufe_record.allowance_total_amount,
        charge_total_amount=cufe_record.charge_total_amount,
        prepaid_amount=cufe_record.prepaid_amount,
        payable_amount=cufe_record.payable_amount,

        # Tax breakdown details
        total_tax_amount=cufe_record.total_tax_amount,
        iva_amount=cufe_record.iva_amount,
        rete_fuente_amount=cufe_record.rete_fuente_amount,
        rete_iva_amount=cufe_record.rete_iva_amount,
        rete_ica_amount=cufe_record.rete_ica_amount,

        # Additional invoice details
        due_date=cufe_record.due_date,
        currency_code=cufe_record.currency_code,
        invoice_type_code=cufe_record.invoice_type_code,
        accounting_cost=cufe_record.accounting_cost,

        # Related data collections
        line_items=line_items if line_items else None,
        allowance_charges=allowance_charges if allowance_charges else None,
        payment_terms=payment_terms if payment_terms else None
    )

# Create database tables on startup
@app.on_event("startup")
async def startup_event():
    """Create database tables on startup"""
    try:
        create_tables()
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Failed to create database tables: {str(e)}")

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "api-service"}

# Authentication endpoints
@app.post("/auth/login", response_model=LoginResponse)
async def login(login_data: LoginRequest, db: Session = Depends(get_db)):
    """
    Authenticate user and return JWT token
    """
    logger.info(f"Login attempt for username: {login_data.username}")
    user = authenticate_user(db, login_data.username, login_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Create access token
    access_token = create_user_token(
        username=user.username,
        user_id=user.id,
        client_id=user.client_id
    )

    return LoginResponse(
        access_token=access_token,
        token_type="bearer",
        user=user,
        client=user.client
    )

@app.get("/auth/me")
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    """Get current user information"""
    return {
        "user": current_user,
        "client": current_user.client
    }

@app.post("/auth/create-test-user")
async def create_test_user(db: Session = Depends(get_db)):
    """Create test user for development - REMOVE IN PRODUCTION"""
    try:
        # Check if test client exists
        test_client = db.query(Client).filter(Client.client_id == "TEST_CLIENT").first()
        if not test_client:
            test_client = Client(
                client_id="TEST_CLIENT",
                company_name="Test Company",
                is_active=True
            )
            db.add(test_client)
            db.commit()
            db.refresh(test_client)

        # Check if test user exists
        test_user = db.query(User).filter(User.username == "testuser").first()
        if not test_user:
            test_user = User(
                username="testuser",
                email="<EMAIL>",
                hashed_password=hash_password("password123"),
                full_name="Test User",
                client_id=test_client.id,
                is_active=True,
                is_admin=False
            )
            db.add(test_user)
            db.commit()

        return {"message": "Test user created", "username": "testuser", "password": "password123"}

    except Exception as e:
        logger.error(f"Error creating test user: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create test user: {str(e)}")

@app.get("/auth/debug")
async def debug_auth(db: Session = Depends(get_db)):
    """Debug endpoint to check users and clients"""
    try:
        users = db.query(User).all()
        clients = db.query(Client).all()

        return {
            "users_count": len(users),
            "clients_count": len(clients),
            "users": [{"username": u.username, "email": u.email, "client_id": u.client_id} for u in users],
            "clients": [{"client_id": c.client_id, "company_name": c.company_name} for c in clients]
        }
    except Exception as e:
        return {"error": str(e)}

# Main API endpoints
@app.post("/process-emails")
async def process_emails(
    request: ProcessEmailsRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Trigger the complete email processing pipeline
    """
    try:
        logger.info(f"Processing emails requested by user: {current_user.username}")

        # Add to background tasks for async processing
        background_tasks.add_task(process_emails_pipeline, request, db, current_user.client_id)

        return {
            "message": "Email processing started",
            "status": "processing",
            "user": current_user.username
        }

    except Exception as e:
        logger.error(f"Error starting email processing: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Processing failed: {str(e)}")

@app.post("/process-emails-sync")
async def process_emails_sync(
    request: ProcessEmailsRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Process emails synchronously (for frontend testing)
    """
    try:
        logger.info(f"Sync processing emails requested by user: {current_user.username}")

        # Call mailbox service directly
        async with httpx.AsyncClient() as client:
            # Convert to mailbox service request format
            mailbox_request = EmailProcessRequest(
                email_host=request.email_host,
                email_port=request.email_port,
                email_username=request.email_username,
                email_password=request.email_password,
                use_ssl=request.use_ssl,
                folder=request.folder,
                date_filter=request.date_filter,
                max_emails=request.max_emails,
                client_id=current_user.client_id
            )

            # Call mailbox service
            response = await client.post(
                f"{MAILBOX_SERVICE_URL}/process-emails",
                json=mailbox_request.dict(),
                timeout=300.0  # 5 minutes timeout
            )

            if response.status_code == 200:
                result = response.json()

                # Process the complete pipeline for sync response
                downloaded_files = result.get('downloaded_files', [])
                all_xml_files = []
                cufe_results = []

                # Process each downloaded ZIP file
                for downloaded_file in downloaded_files:
                    try:
                        # Process ZIP file
                        file_process_response = await client.post(
                            f"{FILE_PROCESSING_SERVICE_URL}/process-zip",
                            json={
                                "file_path": downloaded_file['file_path'],
                                "email_id": downloaded_file.get('email_id'),
                                "preserve_structure": True
                            },
                            timeout=120.0
                        )

                        if file_process_response.status_code == 200:
                            file_result = file_process_response.json()
                            xml_files = file_result.get('xml_files', [])
                            all_xml_files.extend(xml_files)

                            # Extract CUFE from each XML file
                            for xml_file in xml_files:
                                try:
                                    extraction_response = await client.post(
                                        f"{EXTRACTION_SERVICE_URL}/extract-cufe",
                                        json={
                                            "xml_file_path": xml_file['file_path'],
                                            "email_id": xml_file.get('email_id'),
                                            "extract_additional_data": True
                                        },
                                        timeout=60.0
                                    )

                                    if extraction_response.status_code == 200:
                                        extraction_result = extraction_response.json()
                                        if extraction_result.get('success') and extraction_result.get('cufe_value'):
                                            cufe_results.append({
                                                "cufe_value": extraction_result['cufe_value'],
                                                "xml_file": xml_file['filename'],
                                                "issuer_name": extraction_result.get('cufe_data', {}).get('issuer_name'),
                                                "document_number": extraction_result.get('cufe_data', {}).get('document_number'),
                                                "issue_date": extraction_result.get('cufe_data', {}).get('issue_date'),
                                                "total_amount": extraction_result.get('cufe_data', {}).get('total_amount'),
                                                "extraction_date": extraction_result.get('extraction_date')
                                            })
                                except Exception as e:
                                    logger.error(f"Error extracting CUFE from {xml_file['filename']}: {str(e)}")

                    except Exception as e:
                        logger.error(f"Error processing file {downloaded_file['filename']}: {str(e)}")

                # Enhanced response with CUFE information
                enhanced_result = result.copy()
                enhanced_result['extracted_xml_files'] = all_xml_files
                enhanced_result['cufe_extractions'] = cufe_results
                enhanced_result['cufe_count'] = len(cufe_results)

                return {
                    "message": "Email processing completed",
                    "status": "completed",
                    "result": enhanced_result,
                    "user": current_user.username
                }
            else:
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"Mailbox service error: {response.text}"
                )

    except httpx.RequestError as e:
        logger.error(f"Error calling mailbox service: {str(e)}")
        raise HTTPException(status_code=503, detail=f"Mailbox service unavailable: {str(e)}")
    except Exception as e:
        logger.error(f"Error in sync email processing: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Processing failed: {str(e)}")

@app.get("/cufe/{cufe_id}", response_model=CUFEResponse)
async def get_cufe(
    cufe_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get CUFE information by ID (filtered by client)
    """
    try:
        # Query CUFE record filtered by client
        cufe_record = db.query(CUFERecord).join(EmailRecord).filter(
            CUFERecord.cufe_value == cufe_id,
            EmailRecord.client_id == current_user.client_id
        ).first()

        if not cufe_record:
            raise HTTPException(status_code=404, detail="CUFE not found")

        return _convert_cufe_record_to_response(cufe_record, db)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving CUFE {cufe_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"CUFE retrieval failed: {str(e)}")

@app.get("/cufe/", response_model=CUFEListResponse)
async def list_cufe_records(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    List CUFE records for the authenticated user's client
    """
    try:
        # Query CUFE records filtered by client
        cufe_records = db.query(CUFERecord).join(EmailRecord).filter(
            EmailRecord.client_id == current_user.client_id
        ).offset(skip).limit(limit).all()

        total = db.query(CUFERecord).join(EmailRecord).filter(
            EmailRecord.client_id == current_user.client_id
        ).count()

        # Convert to response format
        records = []
        for cufe in cufe_records:
            records.append(_convert_cufe_record_to_response(cufe, db))

        return CUFEListResponse(
            records=records,
            total=total,
            skip=skip,
            limit=limit
        )

    except Exception as e:
        logger.error(f"Error listing CUFE records: {str(e)}")
        raise HTTPException(status_code=500, detail=f"CUFE listing failed: {str(e)}")

@app.delete("/cufe/{cufe_id}")
async def delete_cufe_record(
    cufe_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Delete a CUFE record (filtered by client)
    """
    try:
        # Query CUFE record filtered by client
        cufe_record = db.query(CUFERecord).join(EmailRecord).filter(
            CUFERecord.cufe_value == cufe_id,
            EmailRecord.client_id == current_user.client_id
        ).first()

        if not cufe_record:
            raise HTTPException(status_code=404, detail="CUFE not found")

        # Store info for response
        deleted_cufe = cufe_record.cufe_value
        deleted_issuer = cufe_record.issuer_name

        # Delete the record (cascade will handle related records)
        db.delete(cufe_record)
        db.commit()

        logger.info(f"CUFE record deleted by user {current_user.username}: {deleted_cufe}")

        return {
            "success": True,
            "message": f"Invoice record deleted successfully",
            "deleted_cufe": deleted_cufe,
            "deleted_issuer": deleted_issuer
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting CUFE {cufe_id}: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"CUFE deletion failed: {str(e)}")

@app.get("/download/{file_type}/{cufe_id}")
async def download_file(
    file_type: str,
    cufe_id: str,
    db = Depends(get_db)
):
    """
    Download XML or PDF file associated with a CUFE
    """
    try:
        if file_type not in ["xml", "pdf"]:
            raise HTTPException(status_code=400, detail="File type must be 'xml' or 'pdf'")
        
        # TODO: Get file path from database and return file
        # cufe_record = db.query(CUFERecord).filter(CUFERecord.cufe_value == cufe_id).first()
        
        # Placeholder - return error for now
        raise HTTPException(status_code=404, detail="File not found")
        
    except Exception as e:
        logger.error(f"Error downloading file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"File download failed: {str(e)}")

# Background task function
async def process_emails_pipeline(request: ProcessEmailsRequest, db: Session, client_id: int):
    """
    Background task to process emails through the pipeline
    """
    try:
        logger.info("Starting email processing pipeline")

        # Step 1: Call mailbox service
        async with httpx.AsyncClient() as client:
            mailbox_request = EmailProcessRequest(
                email_host=request.email_host,
                email_port=request.email_port,
                email_username=request.email_username,
                email_password=request.email_password,
                use_ssl=request.use_ssl,
                folder=request.folder,
                date_filter=request.date_filter,
                max_emails=request.max_emails,
                client_id=client_id
            )

            try:
                response = await client.post(
                    f"{MAILBOX_SERVICE_URL}/process-emails",
                    json=mailbox_request.dict(),
                    timeout=300.0
                )

                if response.status_code == 200:
                    mailbox_result = response.json()
                    logger.info(f"Mailbox processing completed: {mailbox_result}")

                    # Step 2: Call file processing service for each downloaded file
                    downloaded_files = mailbox_result.get('downloaded_files', [])
                    all_xml_files = []

                    for downloaded_file in downloaded_files:
                        try:
                            # Process ZIP file
                            file_process_response = await client.post(
                                f"{FILE_PROCESSING_SERVICE_URL}/process-zip",
                                json={
                                    "file_path": downloaded_file['file_path'],
                                    "email_id": downloaded_file.get('email_id'),
                                    "preserve_structure": True
                                },
                                timeout=120.0
                            )

                            if file_process_response.status_code == 200:
                                file_result = file_process_response.json()
                                xml_files = file_result.get('xml_files', [])
                                all_xml_files.extend(xml_files)
                                logger.info(f"Processed ZIP file {downloaded_file['filename']}: {len(xml_files)} XML files extracted")
                            else:
                                logger.error(f"File processing failed for {downloaded_file['filename']}: {file_process_response.text}")

                        except Exception as e:
                            logger.error(f"Error processing file {downloaded_file['filename']}: {str(e)}")

                    # Step 3: Call extraction service for each XML file
                    cufe_results = []

                    for xml_file in all_xml_files:
                        try:
                            # Extract CUFE from XML file
                            extraction_response = await client.post(
                                f"{EXTRACTION_SERVICE_URL}/extract-cufe",
                                json={
                                    "xml_file_path": xml_file['file_path'],
                                    "email_id": xml_file.get('email_id'),
                                    "extract_additional_data": True
                                },
                                timeout=60.0
                            )

                            if extraction_response.status_code == 200:
                                extraction_result = extraction_response.json()
                                if extraction_result.get('success') and extraction_result.get('cufe_value'):
                                    cufe_results.append(extraction_result)
                                    logger.info(f"Extracted CUFE from {xml_file['filename']}: {extraction_result['cufe_value']}")
                                else:
                                    logger.warning(f"No CUFE found in {xml_file['filename']}")
                            else:
                                logger.error(f"CUFE extraction failed for {xml_file['filename']}: {extraction_response.text}")

                        except Exception as e:
                            logger.error(f"Error extracting CUFE from {xml_file['filename']}: {str(e)}")

                    # Step 4: Results are already stored in database by the extraction service
                    logger.info(f"Pipeline completed: {len(cufe_results)} CUFE values extracted")

                else:
                    logger.error(f"Mailbox service error: {response.status_code} - {response.text}")

            except httpx.RequestError as e:
                logger.error(f"Failed to call mailbox service: {str(e)}")

        logger.info("Email processing pipeline completed")

    except Exception as e:
        logger.error(f"Error in email processing pipeline: {str(e)}")

# File download endpoints
@app.get("/download/zip/{file_path:path}")
async def download_zip_file(file_path: str):
    """
    Download a ZIP file by its file path
    """
    try:
        # Decode the URL-encoded file path
        decoded_path = unquote(file_path)

        # Validate file exists and is a ZIP file
        if not os.path.exists(decoded_path):
            raise HTTPException(status_code=404, detail=f"File not found: {decoded_path}")

        if not decoded_path.lower().endswith('.zip'):
            raise HTTPException(status_code=400, detail="Only ZIP files can be downloaded")

        # Get filename for download
        filename = os.path.basename(decoded_path)

        logger.info(f"Serving download for file: {decoded_path}")

        return FileResponse(
            path=decoded_path,
            filename=filename,
            media_type='application/zip'
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error serving file download: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Download failed: {str(e)}")

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=int(os.getenv("PORT", 8000)),
        reload=os.getenv("DEBUG", "false").lower() == "true"
    )
