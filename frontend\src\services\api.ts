import axios, { AxiosInstance, AxiosResponse } from 'axios'
import { LoginRequest, LoginResponse, User, Client } from '../types/auth'

// API Base URL - adjust based on environment
const API_BASE_URL = import.meta.env.VITE_API_URL || '/api'

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle auth errors
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Clear token and redirect to login
      localStorage.removeItem('auth_token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// Auth API
export const authApi = {
  login: async (credentials: LoginRequest): Promise<LoginResponse> => {
    const response: AxiosResponse<LoginResponse> = await apiClient.post('/auth/login', credentials)
    return response.data
  },

  getCurrentUser: async (token?: string): Promise<{ user: User; client: Client }> => {
    const headers = token ? { Authorization: `Bearer ${token}` } : {}
    const response: AxiosResponse<{ user: User; client: Client }> = await apiClient.get('/auth/me', { headers })
    return response.data
  },
}

// CUFE API types
export interface InvoiceLineItem {
  line_number?: number
  item_name?: string
  item_description?: string
  item_code?: string
  invoiced_quantity?: string
  unit_of_measure?: string
  unit_price?: string
  line_extension_amount?: string
  line_tax_amount?: string
  line_tax_inclusive_amount?: string
  allowance_charge_amount?: string
  free_of_charge_indicator?: boolean
}

export interface InvoiceAllowanceCharge {
  charge_indicator: boolean
  allowance_charge_reason_code?: string
  allowance_charge_reason?: string
  multiplier_factor_numeric?: string
  amount?: string
  base_amount?: string
  tax_category?: string
  tax_amount?: string
}

export interface InvoicePaymentTerm {
  payment_means_code?: string
  payment_due_date?: string
  payment_terms_note?: string
  settlement_period_measure?: string
  settlement_period_unit?: string
  settlement_discount_percent?: string
  penalty_surcharge_percent?: string
  amount?: string
}

export interface CUFERecord {
  cufe_value: string
  email_id: string
  reception_date: string
  xml_file_path: string
  pdf_file_path?: string
  processed_date: string

  // Basic invoice information
  issuer_name?: string
  document_number?: string
  issue_date?: string
  total_amount?: string

  // Enhanced tax and monetary details
  tax_exclusive_amount?: string
  tax_inclusive_amount?: string
  allowance_total_amount?: string
  charge_total_amount?: string
  prepaid_amount?: string
  payable_amount?: string

  // Tax breakdown details
  total_tax_amount?: string
  iva_amount?: string
  rete_fuente_amount?: string
  rete_iva_amount?: string
  rete_ica_amount?: string

  // Additional invoice details
  due_date?: string
  currency_code?: string
  invoice_type_code?: string
  accounting_cost?: string

  // Related data collections
  line_items?: InvoiceLineItem[]
  allowance_charges?: InvoiceAllowanceCharge[]
  payment_terms?: InvoicePaymentTerm[]
}

export interface CUFEListResponse {
  records: CUFERecord[]
  total: number
  skip: number
  limit: number
}

// CUFE API
export const cufeApi = {
  getCUFERecords: async (skip = 0, limit = 100): Promise<CUFEListResponse> => {
    const response: AxiosResponse<CUFEListResponse> = await apiClient.get('/cufe/', {
      params: { skip, limit }
    })
    return response.data
  },

  getCUFEById: async (cufeId: string): Promise<CUFERecord> => {
    const response: AxiosResponse<CUFERecord> = await apiClient.get(`/cufe/${cufeId}`)
    return response.data
  },

  deleteCUFE: async (cufeId: string): Promise<{ success: boolean; message: string; deleted_cufe: string; deleted_issuer: string }> => {
    const response: AxiosResponse<{ success: boolean; message: string; deleted_cufe: string; deleted_issuer: string }> = await apiClient.delete(`/cufe/${cufeId}`)
    return response.data
  },
}

// File Processing API types
export interface ProcessEmailsRequest {
  email_host: string
  email_port: number
  email_username: string
  email_password: string
  use_ssl: boolean
  folder: string
  date_filter?: string
  max_emails?: number
}

export interface ProcessEmailsResponse {
  success: boolean
  message: string
  processed_count?: number
  downloaded_files?: any[]
  errors?: string[]
}

export interface FileUploadResponse {
  filename: string
  file_path: string
  message: string
}

// File Processing API
export const fileProcessingApi = {
  processEmails: async (request: ProcessEmailsRequest): Promise<ProcessEmailsResponse> => {
    const response: AxiosResponse<ProcessEmailsResponse> = await apiClient.post('/process-emails', request)
    return response.data
  },

  processEmailsSync: async (request: ProcessEmailsRequest): Promise<ProcessEmailsResponse> => {
    const response: AxiosResponse<ProcessEmailsResponse> = await apiClient.post('/process-emails-sync', request)
    return response.data
  },

  uploadFile: async (file: File): Promise<FileUploadResponse> => {
    const formData = new FormData()
    formData.append('file', file)

    const response: AxiosResponse<FileUploadResponse> = await apiClient.post('/upload-zip', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
    return response.data
  },

  processZipFile: async (filePath: string, emailId?: string): Promise<any> => {
    const response = await apiClient.post('/process-zip', {
      file_path: filePath,
      email_id: emailId,
      preserve_structure: true
    })
    return response.data
  },
}

// Health check
export const healthApi = {
  check: async (): Promise<{ status: string; service: string }> => {
    const response = await apiClient.get('/health')
    return response.data
  },
}

export default apiClient
